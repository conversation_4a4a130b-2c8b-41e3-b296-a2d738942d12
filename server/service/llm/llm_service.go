package llm

import (
	context "context"
	"encoding/json"
	"errors"
	"io"
	"math"
	"os"
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/integral"
	llmModel "github.com/flipped-aurora/gin-vue-admin/server/model/llm"
	llmReq "github.com/flipped-aurora/gin-vue-admin/server/model/llm/request"
	llmResp "github.com/flipped-aurora/gin-vue-admin/server/model/llm/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/pkoukk/tiktoken-go"
	"github.com/sashabaranov/go-openai"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// LLMService 支持多模型调用，自动根据模型名查库获取密钥和配置信息
// 支持多厂商扩展（如 openai, azure, baidu 等）
type LLMService struct{}

// validateAndFixToolSchema 验证并修复工具参数 schema
func validateAndFixToolSchema(toolName string, params map[string]interface{}) map[string]interface{} {
	if params == nil {
		return map[string]interface{}{
			"type":       "object",
			"properties": map[string]interface{}{},
		}
	}

	// 如果已经是正确的 object schema 格式，直接返回
	if schemaType, ok := params["type"].(string); ok && schemaType == "object" {
		if _, hasProps := params["properties"]; hasProps {
			return params
		}
	}

	// 检查是否是单个参数的定义（包含 type 和 description）
	if paramType, hasType := params["type"]; hasType {
		if description, hasDesc := params["description"]; hasDesc {
			// 这是一个单个参数的定义，需要包装成完整的 schema
			paramName := toolName
			if paramName == "" {
				paramName = "value"
			}
			return map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					paramName: map[string]interface{}{
						"type":        paramType,
						"description": description,
					},
				},
				"required": []string{paramName},
			}
		}
	}

	// 默认情况：将整个 params 作为 properties 包装
	return map[string]interface{}{
		"type":       "object",
		"properties": params,
	}
}

// handleLLMPointsAndLog 封装积分扣除、流水和用量日志写入（事务）
func handleLLMPointsAndLog(tx *gorm.DB, user *system.SysUser, userId uint, points int, modelInfo *llmModel.LLMModel, req *llmReq.LLMRequest, resp *llmResp.LLMResponse) error {
	// 扣除积分
	if err := tx.Model(user).Update("points", gorm.Expr("points - ?", points)).Error; err != nil {
		return err
	}
	// 写入 LLMUsageLog
	usageLog := llmModel.LLMUsageLog{
		ModelID:          modelInfo.ID,
		UserID:           userId,
		Prompt:           req.Messages[0].Content,         // 可拼接所有内容
		Response:         resp.Choices[0].Message.Content, // 可拼接所有内容
		PromptTokens:     resp.Usage.PromptTokens,
		CompletionTokens: resp.Usage.CompletionTokens,
		TotalTokens:      resp.Usage.TotalTokens,
	}
	if err := tx.Create(&usageLog).Error; err != nil {
		return err
	}
	// 写入积分流水
	if err := tx.Create(&integral.SysUserPoints{
		UserID:     userId,
		Change:     -points,
		Reason:     "模型调用消耗",
		Type:       "llm",
		ModelID:    modelInfo.ID,
		UsageLogID: usageLog.ID,
	}).Error; err != nil {
		return err
	}
	return nil
}

// ChatStream 支持流式响应的大语言模型对话
func (s *LLMService) ChatStream(ctx context.Context, req *llmReq.LLMRequest, userId uint, streamHandler func(*llmResp.LLMResponse) error) error {
	if req == nil {
		return errors.New("request is nil")
	}
	// 1. 查询数据库获取模型信息
	var modelInfo llmModel.LLMModel
	err := global.GVA_DB.Where("name = 'gpt-4.1-nano-2025-04-14' AND is_enabled = ?", true).First(&modelInfo).Error
	if err != nil {
		return errors.New("模型未配置或已禁用: " + req.Model)
	}

	if userId > 0 {
		// 2. 查询用户积分，先判断是否大于等于最低消耗积分
		var user system.SysUser
		if err := global.GVA_DB.First(&user, userId).Error; err != nil {
			return errors.New("用户不存在")
		}
		// 计费参数选择
		var creditCost, tokenPerPoint int
		if user.VipLevel > 0 {
			creditCost = modelInfo.VipCreditCost
			tokenPerPoint = modelInfo.VipTokenPerPoint
		} else {
			creditCost = modelInfo.CreditCost
			tokenPerPoint = modelInfo.TokenPerPoint
		}
		if user.Points+user.FreePoints < 0 {
			return errors.New("体力不足")
		}
		if user.Points+user.FreePoints < creditCost {
			return errors.New("积分不足，最低消耗积分为" + strconv.Itoa(creditCost))
		}
		return streamOpenAI(ctx, req, &modelInfo, &user, userId, streamHandler, creditCost, tokenPerPoint)
	} else {
		// 游客模式，不校验积分，不扣积分，仅写用量日志
		return streamOpenAI(ctx, req, &modelInfo, nil, 0, streamHandler, 0, 0)
	}
}

// streamOpenAI 使用 go-openai v1.40.0 实现流式响应
func streamOpenAI(ctx context.Context, req *llmReq.LLMRequest, modelInfo *llmModel.LLMModel, user *system.SysUser, userId uint, streamHandler func(*llmResp.LLMResponse) error, creditCost int, tokenPerPoint int) error {
	config := openai.DefaultConfig(modelInfo.ApiKey)
	if modelInfo.BaseURL != "" {
		config.BaseURL = modelInfo.BaseURL
	}
	client := openai.NewClientWithConfig(config)

	messages := make([]openai.ChatCompletionMessage, 0, len(req.Messages))
	promptText := ""
	for _, m := range req.Messages {
		// 跳过空内容的 assistant 消息，避免 OpenAI API 返回 500 错误
		if m.Role == "assistant" && m.Content == "" {
			continue
		}

		messages = append(messages, openai.ChatCompletionMessage{
			Role:    m.Role,
			Content: m.Content,
		})
		promptText += m.Content
	}
	// 统计 prompt token
	promptTokens := 0
	if enc, err := tiktoken.EncodingForModel(req.Model); err == nil {
		promptTokens = len(enc.Encode(promptText, nil, nil))
	}

	stream, err := client.CreateChatCompletionStream(ctx, openai.ChatCompletionRequest{
		Model:            req.Model,
		Messages:         messages,
		MaxTokens:        req.MaxTokens,
		Temperature:      float32(req.Temperature),
		TopP:             float32(req.TopP),
		Stream:           true,
		Stop:             req.Stop,
		PresencePenalty:  req.PresencePenalty,
		FrequencyPenalty: req.FrequencyPenalty,
		User:             req.User,
	})
	if err != nil {
		return err
	}
	defer stream.Close()

	var fullContent string
	for {
		resp, err := stream.Recv()
		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}
		if resp.Choices == nil || len(resp.Choices) == 0 {
			continue
		}
		fullContent += resp.Choices[0].Delta.Content
		partial := &llmResp.LLMResponse{
			ID:      resp.ID,
			Object:  resp.Object,
			Created: resp.Created,
			Model:   resp.Model,
			Choices: []llmResp.Choice{{
				Index:        resp.Choices[0].Index,
				Message:      llmReq.Message{Role: resp.Choices[0].Delta.Role, Content: resp.Choices[0].Delta.Content},
				FinishReason: string(resp.Choices[0].FinishReason),
			}},
		}
		if err := streamHandler(partial); err != nil {
			return err
		}
	}
	// 统计 completion token
	completionTokens := 0
	if enc, err := tiktoken.EncodingForModel(req.Model); err == nil {
		completionTokens = len(enc.Encode(fullContent, nil, nil))
	}
	totalTokens := promptTokens + completionTokens

	finalResp := &llmResp.LLMResponse{
		ID:      "",
		Object:  "chat.completion",
		Created: 0,
		Model:   req.Model,
		Choices: []llmResp.Choice{{
			Index:   0,
			Message: llmReq.Message{Role: "assistant", Content: fullContent},
		}},
		Usage: &llmResp.Usage{
			PromptTokens:     promptTokens,
			CompletionTokens: completionTokens,
			TotalTokens:      totalTokens,
		},
	}

	if tokenPerPoint <= 0 {
		tokenPerPoint = 100
	}
	points := int(math.Ceil(float64(totalTokens) / float64(tokenPerPoint)))
	if points < creditCost {
		points = creditCost
	}
	if user != nil && user.Points < points {
		points = user.Points
	}

	if user != nil {
		// 扣除积分并写入流水和用量日志（事务）
		err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
			return handleLLMPointsAndLog(tx, user, userId, points, modelInfo, req, finalResp)
		})
		if err != nil {
			return err
		}
	}

	return nil
}

// FunctionCallStream 只流式透传大模型返回的 tool_calls 和 content 分片，不做本地函数执行
func (s *LLMService) FunctionCallStream(ctx context.Context, req *llmReq.LLMRequest, userId uint, streamHandler func(*llmResp.LLMResponse) error) error {
	if req == nil {
		return errors.New("request is nil")
	}
	// 1. 查询数据库获取模型信息
	var modelInfo llmModel.LLMModel
	err := global.GVA_DB.Where("name = 'gpt-4.1-nano-2025-04-14' AND is_enabled = ?", true).First(&modelInfo).Error
	if err != nil {
		return errors.New("模型未配置或已禁用: " + req.Model)
	}

	var user *system.SysUser
	var creditCost, tokenPerPoint int
	if userId > 0 {
		u := &system.SysUser{}
		if err := global.GVA_DB.First(u, userId).Error; err != nil {
			return errors.New("用户不存在")
		}
		user = u
		if user.VipLevel > 0 {
			creditCost = modelInfo.VipCreditCost
			tokenPerPoint = modelInfo.VipTokenPerPoint
		} else {
			creditCost = modelInfo.CreditCost
			tokenPerPoint = modelInfo.TokenPerPoint
		}
		if user.Points+user.FreePoints < 0 {
			return errors.New("体力不足")
		}
		if user.Points+user.FreePoints < creditCost {
			return errors.New("积分不足，最低消耗积分为" + strconv.Itoa(creditCost))
		}
	}

	// 2. 统计 prompt token
	promptText := ""
	for _, m := range req.Messages {
		promptText += m.Content
	}
	promptTokens := 0
	if enc, err := tiktoken.EncodingForModel(req.Model); err == nil {
		promptTokens = len(enc.Encode(promptText, nil, nil))
	}

	// 3. 构造OpenAI请求参数
	config := openai.DefaultConfig(modelInfo.ApiKey)
	if modelInfo.BaseURL != "" {
		config.BaseURL = modelInfo.BaseURL
	}
	client := openai.NewClientWithConfig(config)

	// 读取系统提示词文件
	systemPrompt := ""
	// 尝试多个可能的路径，以支持从不同目录启动应用
	promptPaths := []string{
		"agent-text-prompt.txt", // 从 server/ 目录启动时
	}
	for _, path := range promptPaths {
		if promptData, err := os.ReadFile(path); err == nil {
			systemPrompt = string(promptData)
			break
		}
	}

	// 构造消息数组，首先添加系统消息
	messages := make([]openai.ChatCompletionMessage, 0, len(req.Messages)+1)
	if systemPrompt != "" {
		messages = append(messages, openai.ChatCompletionMessage{
			Role:    "system",
			Content: systemPrompt,
		})
	}

	// 添加用户消息
	for _, m := range req.Messages {
		// 跳过空内容的 assistant 消息，避免 OpenAI API 返回 500 错误
		if m.Role == "assistant" && m.Content == "" && m.FunctionCall == nil && len(m.ToolCalls) == 0 {
			continue
		}

		message := openai.ChatCompletionMessage{
			Role:    m.Role,
			Content: m.Content,
			Name:    m.Name,
		}
		if m.FunctionCall != nil {
			message.FunctionCall = &openai.FunctionCall{
				Name:      m.FunctionCall.Name,
				Arguments: m.FunctionCall.Arguments,
			}
		}
		messages = append(messages, message)
	}

	// tools 转换
	var tools []openai.Tool
	for _, t := range req.Tools {
		// 记录原始工具定义用于调试
		if originalParams, err := json.Marshal(t.Parameters); err == nil {
			global.GVA_LOG.Debug("Processing tool", zap.String("name", t.Name), zap.String("original_params", string(originalParams)))
		}

		// 使用新的验证和修复函数
		params := validateAndFixToolSchema(t.Name, t.Parameters)

		// 记录处理后的参数用于调试
		if processedParams, err := json.Marshal(params); err == nil {
			global.GVA_LOG.Debug("Processed tool params", zap.String("name", t.Name), zap.String("processed_params", string(processedParams)))
		}

		if t.Type == "function" || t.Type == "" {
			tools = append(tools, openai.Tool{
				Type: openai.ToolTypeFunction,
				Function: &openai.FunctionDefinition{
					Name:        t.Name,
					Description: t.Description,
					Parameters:  params,
				},
			})
		}
	}

	var toolChoice any
	if req.ToolChoice != nil {
		toolChoice = req.ToolChoice
	}

	stream, err := client.CreateChatCompletionStream(ctx, openai.ChatCompletionRequest{
		Model:            modelInfo.Name,
		Messages:         messages,
		MaxTokens:        req.MaxTokens,
		Temperature:      float32(req.Temperature),
		TopP:             float32(req.TopP),
		Stream:           true,
		Stop:             req.Stop,
		PresencePenalty:  req.PresencePenalty,
		FrequencyPenalty: req.FrequencyPenalty,
		User:             req.User,
		Tools:            tools,
		ToolChoice:       toolChoice,
	})
	if err != nil {
		return err
	}
	defer stream.Close()

	// 4. 收集所有content分片和tool_calls分片（分片组装）
	var fullContent string
	toolCallMap := map[int]*llmReq.ToolCall{}
	toolCallOrder := []int{}
	for {
		resp, err := stream.Recv()
		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}
		if resp.Choices == nil || len(resp.Choices) == 0 {
			continue
		}
		choice := resp.Choices[0]
		// 收集content分片
		if choice.Delta.Content != "" {
			fullContent += choice.Delta.Content
		}
		// 组装tool_calls分片
		if len(choice.Delta.ToolCalls) > 0 {
			for _, tc := range choice.Delta.ToolCalls {
				idx := 0
				if tc.Index != nil {
					idx = *tc.Index
				}
				if toolCallMap[idx] == nil {
					toolCallMap[idx] = &llmReq.ToolCall{
						ID:       tc.ID,
						Type:     string(tc.Type),
						Function: &llmReq.FunctionCall{},
						Index:    tc.Index,
					}
					toolCallOrder = append(toolCallOrder, idx)
				}
				if tc.ID != "" {
					toolCallMap[idx].ID = tc.ID
				}
				if tc.Function.Name != "" {
					toolCallMap[idx].Function.Name = tc.Function.Name
				}
				if tc.Function.Arguments != "" {
					toolCallMap[idx].Function.Arguments += tc.Function.Arguments
				}
			}
			// 透传分片（组装后的tool_calls）
			var toolCallsArr []llmReq.ToolCall
			for _, idx := range toolCallOrder {
				toolCallsArr = append(toolCallsArr, *toolCallMap[idx])
			}
			partial := &llmResp.LLMResponse{
				ID:      resp.ID,
				Object:  resp.Object,
				Created: resp.Created,
				Model:   resp.Model,
				Choices: []llmResp.Choice{{
					Index:        choice.Index,
					Delta:        &llmResp.Delta{ToolCalls: toolCallsArr},
					FinishReason: string(choice.FinishReason),
				}},
			}
			if err := streamHandler(partial); err != nil {
				return err
			}
		}
		// 普通内容分片也透传
		if choice.Delta.Content != "" {
			partial := &llmResp.LLMResponse{
				ID:      resp.ID,
				Object:  resp.Object,
				Created: resp.Created,
				Model:   resp.Model,
				Choices: []llmResp.Choice{{
					Index:        choice.Index,
					Delta:        &llmResp.Delta{Content: choice.Delta.Content, Role: choice.Delta.Role},
					FinishReason: string(choice.FinishReason),
				}},
			}
			if err := streamHandler(partial); err != nil {
				return err
			}
		}
	}

	// 5. 统计 completion token（content + 所有 tool_call arguments）
	var allOutput string = fullContent
	for _, idx := range toolCallOrder {
		if tc := toolCallMap[idx]; tc != nil && tc.Function != nil {
			allOutput += tc.Function.Arguments
		}
	}
	completionTokens := 0
	if enc, err := tiktoken.EncodingForModel(req.Model); err == nil {
		completionTokens = len(enc.Encode(allOutput, nil, nil))
	}
	totalTokens := promptTokens + completionTokens

	// 6. 日志内容优先用fullContent，否则用组装好的tool_calls JSON
	responseContent := fullContent
	if responseContent == "" && len(toolCallOrder) > 0 {
		var toolCallsArr []llmReq.ToolCall
		for _, idx := range toolCallOrder {
			toolCallsArr = append(toolCallsArr, *toolCallMap[idx])
		}
		if b, err := json.Marshal(toolCallsArr); err == nil {
			responseContent = string(b)
		}
	}

	finalResp := &llmResp.LLMResponse{
		ID:      "",
		Object:  "chat.completion",
		Created: 0,
		Model:   req.Model,
		Choices: []llmResp.Choice{{
			Index:   0,
			Message: llmReq.Message{Role: "assistant", Content: responseContent},
		}},
		Usage: &llmResp.Usage{
			PromptTokens:     promptTokens,
			CompletionTokens: completionTokens,
			TotalTokens:      totalTokens,
		},
	}

	if tokenPerPoint <= 0 {
		tokenPerPoint = 100
	}
	points := int(math.Ceil(float64(totalTokens) / float64(tokenPerPoint)))
	if points < creditCost {
		points = creditCost
	}
	if user != nil && user.Points < points {
		points = user.Points
	}

	if user != nil {
		// 扣除积分并写入流水和用量日志（事务）
		err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
			return handleLLMPointsAndLog(tx, user, userId, points, &modelInfo, req, finalResp)
		})
		if err != nil {
			return err
		}
	}

	return nil
}
